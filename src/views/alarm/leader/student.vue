<template>
  <div class="bg-[#f0f2f9] h-full w-full flex flex-col">
    <div class="bg-white">
      <p class="bg-[#FFF8F0] p-2.5 text-[#EC8800] text-[24px] font-normal font-pingfang">【温馨提示】学生毕业的最终解释权归四川邮电职业技术学院所有</p>
      <SearchItem v-model:searchParams="searchParams" :options="searchOptions" />
      <TabsItem :options="tabsOptions" v-model:businessActive="businessActive" />
    </div>
    <div class="flex-1 min-h-0 py-[24px]">
      <div class="h-full overflow-y-auto px-[26px]">
        <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="flex flex-col gap-y-[30px]">
            <ListItem v-for="item in list" :key="item" :options="listOptionsKey">
              <template #title>
                <span>预警班级：{{ item }}</span>
              </template>
            </ListItem>
          </div>
        </van-list>
      </div>
    </div>
  </div>
</template>

<script setup>
import ListItem from "@/views/todo/components/list-item.vue";
import SearchItem from "@/views/todo/components/search-item.vue";
import TabsItem from "@/views/todo/components/tabs-item.vue";
defineOptions({
  name: "LeaderStudent",
  title: "校领导-学生",
});
definePage({
  name: "LeaderStudent",
  meta: {
    layout: "index",
    title: "预警信息",
    navBar: true,
    isAuth: false,
  },
});

// 业务类型选择
const businessActive = ref("bx");
const listOptionsKey = computed(() => {
  return listOptions.value[tabsOptions.value.find((item) => item.key === businessActive.value)?.listOptionsKey];
});
// 业务类型选择选项
const tabsOptions = ref([
  {
    label: "必修",
    value: "1",
    key: "bx",
    listOptionsKey: "bx",
  },
  {
    label: "选修",
    value: "2",
    key: "xx",
    listOptionsKey: "xx",
  },
  {
    label: "实习",
    value: "3",
    key: "sx",
    listOptionsKey: "bx",
  },
  {
    label: "毕设",
    value: "4",
    key: "bs",
    listOptionsKey: "bx",
  },
]);

// 搜索参数
const searchParams = ref({
  grade: "",
  class: "",
});
// 搜索参数选项
const searchOptions = ref([
  {
    key: "yx", //获取选择数据源
    label: "院系",
    value: "yx",
    type: "select",
    placeholder: "全部院系",
  },
  {
    key: "zy", //获取选择数据源
    label: "专业",
    value: "zy",
    type: "select",
    placeholder: "全部专业",
  },
  {
    key: "nj", //获取选择数据源
    label: "年级",
    value: "grade",
    type: "select",
    placeholder: "全部年级",
  },
  {
    key: "bj",
    label: "班级",
    value: "class",
    type: "select",
    placeholder: "全部班级",
  },
]);

// 列表选项
// 列表选项
const listOptions = ref({
  bx: [
    {
      label: "预警课程",
      value: "value",
    },
    {
      label: "开课学年/学期",
      value: "value",
    },
    {
      label: "实得成绩",
      value: "value",
    },
    {
      label: "成绩类型",
      value: "value",
    },
  ],
  xx: [
    {
      label: "已修课程",
      value: "value",
    },
    {
      label: "待修课程",
      value: "value",
    },
  ],
});
// 列表
const list = ref([]);
// 加载状态
const loading = ref(false);
// 是否加载完成
const finished = ref(false);
const onLoad = () => {
  // 异步更新数据
  // setTimeout 仅做示例，真实场景中一般为 ajax 请求
  setTimeout(() => {
    for (let i = 0; i < 10; i++) {
      list.value.push(list.value.length + 1);
    }
    // 加载状态结束
    loading.value = false;
    // 数据全部加载完成
    if (list.value.length >= 40) {
      finished.value = true;
    }
  }, 1000);
};
const onRefresh = () => {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  onLoad();
};
// 监听业务类型选择
watch(businessActive, (newVal) => {
  list.value = [];
  onRefresh();
});
</script>

<style lang="scss" scoped></style>
