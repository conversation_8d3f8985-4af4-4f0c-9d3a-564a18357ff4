<template>
  <div class="alarm-student h-full w-full flex flex-col">
    <div class="bg-white">
      <p class="bg-[#FDE9EB] p-2.5 text-[#C13C30] text-[24px] indent-2.5 font-normal font-pingfang">
        【温馨提示】根据您的考试成绩，教务系统考核您现在未达到毕业条件，请持续关注您的学业信息。（以下预警信息基于教务系统产生，最终解释权在教务处。如有疑问，请您登录教务系统于对应的查询界面进行核实，所有数据以教务系统为准）。
      </p>
      <TabsItem :options="tabsOptions" v-model:businessActive="businessActive" />
    </div>
    <div class="flex-1 min-h-0 py-[24px]">
      <div class="h-full overflow-y-auto px-[26px]">
        <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="flex flex-col gap-y-[30px]">
            <ListItem v-for="item in list" :key="item"></ListItem>
          </div>
        </van-list>
      </div>
    </div>
  </div>
</template>

<script setup>
import TabsItem from "@/views/todo/components/tabs-item.vue";
import ListItem from "@/views/todo/components/list-item.vue";
defineOptions({
  name: "AlarmStudent",
  title: "预警信息-学生",
});
definePage({
  name: "AlarmStudent",
  meta: {
    layout: "index",
    title: "预警信息",
    navBar: true,
    isAuth: false,
  },
});
const tabsOptions = ref([
  {
    label: "必修",
    value: "1",
    key: "必修",
  },
  {
    label: "选修",
    value: "2",
    key: "选修",
  },
  {
    label: "实习",
    value: "3",
    key: "实习",
  },
  {
    label: "毕设",
    value: "4",
    key: "毕设",
  },
]);

const businessActive = ref("必修");
const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const onLoad = () => {
  // 异步更新数据
  // setTimeout 仅做示例，真实场景中一般为 ajax 请求
  setTimeout(() => {
    for (let i = 0; i < 10; i++) {
      list.value.push(list.value.length + 1);
    }
    // 加载状态结束
    loading.value = false;
    // 数据全部加载完成
    if (list.value.length >= 40) {
      finished.value = true;
    }
  }, 1000);
};
const onRefresh = () => {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  onLoad();
};
watch(businessActive, (newVal) => {
  list.value = [];
  onRefresh();
});
</script>

<style lang="scss" scoped>
.alarm-student {
  background: #f0f2f9;
  font-size: 14px;
  .business-tabs {
    &::after {
      content: "";
      position: absolute;
      left: var(--indicator-left, 12.5%);
      bottom: 0;
      width: 24px;
      height: 4px;
      background: #0e5fff;
      transform: translateX(-50%);
      transition: all 0.3s ease-in-out;
    }
  }
}
</style>
