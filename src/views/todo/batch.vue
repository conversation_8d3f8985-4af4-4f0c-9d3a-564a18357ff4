<template>
  <div class="todo-details bg-[#F0F2F9] w-full h-full box-border pt-3.75 flex flex-col">
    <div class="overflow-y-auto flex-1 min-h-0">
      <van-collapse v-model="activeNames" class="custom-collapse">
        <van-collapse-item name="1">
          <template #title>
            <p class="custom-collapse-title text-[32px] text-[#333] font-pingfang relative">预警信息</p>
          </template>
          <div class="p-3.75 flex flex-col gap-y-3.75">
            <CellItem :options="yjxxOptions">
              <!-- 预警人数 -->
              <template #yjrsSlot>
                <p class="text-[#FF3939] text-[30px] mt-[20px]">12人</p>
              </template>
              <!-- 预警课程数 -->
              <template #yjkcsSlot>
                <p class="text-[#FF3939] text-[30px] mt-[20px]">6门</p>
              </template>
            </CellItem>
          </div>
        </van-collapse-item>
        <div class="mt-5 bg-white">
          <p
            class="custom-collapse-title py-[32px] text-[32px] text-[#333] font-pingfang relative border-b border-[#E5E5E5]">
            人员课程信息</p>
          <div class="px-[30px]">
            <van-collapse-item name="2" class="kc-item">
              <template #title>
                <p class="flex items-center gap-x-1.5">
                  <img :src="$getAssetsFile('card-kcxx-icon.png')" alt="" class="w-[30px] h-[30px]" />
                  <span class="text-base text-[#333] text-[28px] font-pingfang">必修、实习、毕设课程信息</span>
                </p>
              </template>
              <div class="flex flex-col gap-y-3.75 pb-[30px]">
                <div v-for="item in 10" :key="item" class="bg-[#F4F5F9] px-[30px] py-[20px]">
                  <div class="flex items-center">
                    <p class="text-white px-1.25 py-[1px] rounded-[4px] bg-[#1181FF] text-xs font-bold">
                      {{ item }}
                    </p>
                    <span class="pl-1.5 text-[#222] text-[30px] font-medium font-pingfang">张天其</span>
                    <span class="pl-3 pr-1.5 text-[#2A69B0] text-[24px] font-normal font-pingfang">2022通信工程 （本科）</span>
                    <span class="border-2 border-[#979797] py-0.75"></span>
                    <span class="pl-1.5 text-[#2A69B0] text-[24px] font-normal font-pingfang">10978490</span>
                  </div>
                  <div class="mt-[26px] w-full box-border rounded-[4px] flex items-center justify-between">
                    <p class="w-[414px] text-[#343434] text-[24px] font-pingfang font-medium">宽带接入工程实训</p>
                    <p>
                      <span class="text-lg font-black font-['DINPro-Bold'] text-[#FF3232]">60</span>
                      <span class="text-[#B9B9B9] text-[24px] font-pingfang">分</span>
                    </p>
                  </div>
                </div>
              </div>
            </van-collapse-item>
          </div>
          <div class="border-t border-[#E5E5E5] px-[30px]">
            <van-collapse-item name="3" class="kc-item">
              <template #title>
                <p class="flex items-center gap-x-1.5">
                  <img :src="$getAssetsFile('card-kcxx-icon.png')" alt="" class="w-[30px] h-[30px]" />
                  <span class="text-base text-[#333] text-[28px] font-pingfang">选修课程信息</span>
                </p>
              </template>
              <div class="flex flex-col gap-y-3.75 pb-[30px]">
                <div v-for="item in 10" :key="item" class="bg-[#F4F5F9] px-[30px] py-[20px]">
                  <div class="flex items-center">
                    <p class="text-white px-1.25 py-[1px] rounded-[4px] bg-[#1181FF] text-xs font-bold">
                      {{ item }}
                    </p>
                    <span class="pl-1.5 text-[#222] text-[30px] font-medium font-pingfang">张天其</span>
                    <span class="pl-3 pr-1.5 text-[#2A69B0] text-[24px] font-normal font-pingfang">2022通信工程 （本科）</span>
                    <span class="border-2 border-[#979797] py-0.75"></span>
                    <span class="pl-1.5 text-[#2A69B0] text-[24px] font-normal font-pingfang">10978490</span>
                  </div>
                  <div class="mt-[26px] w-full box-border rounded-[4px] flex items-center justify-between">
                    <div class="flex items-center w-[200px] justify-between">
                      <p class="text-[#343434] text-[24px] font-pingfang font-medium">已修</p>
                      <p class="flex items-center gap-x-0.5">
                        <span class="text-[#343434] text-[36px] font-black">10</span>
                        <span class="text-[24px] text-[#B9B9B9] font-pingfang translate-y-0.25">门</span>
                      </p>
                    </div>
                    <div class="flex items-center w-[200px] justify-between">
                      <p class="text-[#343434] text-[24px] font-pingfang font-medium">待修</p>
                      <p class="flex items-center gap-x-0.5">
                        <span class="text-[#FF3939] text-[36px] font-black">10</span>
                        <span class="text-[24px] text-[#B9B9B9] font-pingfang translate-y-0.25">门</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </van-collapse-item>
          </div>
        </div>
      </van-collapse>
      <div class="mt-5 bg-white">
        <p class="custom-collapse-title relative h-[104px] flex items-center border-b border-[#E5E5E5]">
          <span class="text-[32px] text-[#333] font-pingfang">预警处理</span>
        </p>
        <div class="p-3.75">
          <p class="required relative text-[#999] text-[30px] font-normal font-pingfang">干预措施</p>
          <van-field v-model="message" class="tetx-[30px] text-[#222] !py-2.5 !px-[18px] font-pingfang" rows="2"
            autosize
            type="textarea" maxlength="500" show-word-limit placeholder="请输入" />
        </div>
      </div>
      <div class="mt-5 bg-white p-3.75">
        <p class="text-[#999] text-[30px] font-normal font-pingfang">再次提醒日期</p>
        <p class="pt-2.5 flex items-center justify-between" @click="dateAgainShow = true">
          <span class="text-[#222] text-[30px] font-normal font-pingfang">2017-03-25</span>
          <van-icon name="arrow" size="20" color="#999" />
        </p>
      </div>

      <div class="mt-5 bg-white p-3.75 mb-3.75">
        <p class="text-[#999] text-[30px] font-normal font-pingfang">处理时间</p>
        <p class="mt-2.5 text-[#222] text-[30px] font-normal font-pingfang">2025-07-21</p>
      </div>
    </div>
    <div class="bg-white p-3.75">
      <van-button block type="primary" size="small">保 存</van-button>
    </div>
    <van-popup v-model:show="dateAgainShow" position="bottom" teleport="body">
      <van-date-picker title="选择日期" :min-date="minDate" :max-date="maxDate" @confirm="onConfirm"
        @cancel="dateAgainShow = false">
        <template #confirm>
          <span class="text-[#0E5FFF] font-pingfang active:text-[#1d64f2]">确定</span>
        </template>
      </van-date-picker>
    </van-popup>
  </div>
</template>

<script setup>
import CellItem from "./components/cell-item.vue";
defineOptions({
  name: "Batch",
  title: "批量处理",
});
definePage({
  name: "Batch",
  meta: {
    layout: "index",
    title: "批量处理",
    navBar: true,
    isAuth: false,
  },
});

const activeNames = ref([]);
const message = ref("");

const yjxxOptions = ref([
  {
    title: "预警时间",
    value: "",
  },
  {
    title: "预警院系",
    value: "",
  },
  {
    title: "预警专业",
    value: "",
  },
  {
    title: "预警年级",
    value: "",
  },
  {
    title: "预警人数",
    value: "",
    slot: "yjrsSlot",
  },
  {
    title: "预警课程数",
    value: "",
    slot: "yjkcsSlot",
  },
]);

// 再次提醒日期
const minDate = ref(new Date());
const maxDate = ref(new Date(2099, 11, 31));
const dateAgainShow = ref(false);
const onConfirm = (date) => {
  console.log(date);
  dateAgainShow.value = false;
};
</script>

<style lang="scss" scoped>
.todo-details {
  .custom-collapse {
    ::v-deep(.van-cell) {
      padding: 0 30px 0 0;
      height: 104px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &::after {
        content: none;
      }
    }

    ::v-deep(.van-collapse-item__wrapper) {
      margin-top: 2px;
    }

    ::v-deep(.van-collapse-item__content) {
      padding: 0;
    }

    ::v-deep(.van-collapse-item--border:after) {
      content: none;
    }

    ::v-deep(.kc-item .van-cell--clickable) {
      padding: 0;
    }

    ::v-deep(.van-cell--clickable:active) {
      background-color: transparent;
    }
  }

  .custom-collapse-title {
    padding-left: 28px;

    &::after {
      content: "";
      position: absolute;
      width: 10px;
      height: 38px;
      background: linear-gradient(180deg, #4ca6ff 0%, #256dff 100%);
      box-shadow: 0px 4px 8px 0px #256dff;
      border-radius: 0px 8px 8px 0px;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .required {
    padding-left: 18px;

    &::after {
      content: "*";
      position: absolute;
      width: 18px;
      left: 0px;
      top: 50%;
      transform: translateY(-50%);
      height: 40px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 32px;
      color: #ff3232;
      line-height: 40px;
      text-align: center;
    }
  }
}
</style>
