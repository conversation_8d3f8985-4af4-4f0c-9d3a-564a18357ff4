<template>
  <div class="business-tabs h-[88px] relative flex items-center" :style="{ '--indicator-left': businessIndicatorLeft }">
    <p
      class="flex-1 text-center text-[#222] text-[28px] font-normal font-pingfang"
      :class="{ '!text-[#256DFF]': businessActive === item.key }"
      @click="businessActive = item.key"
      v-for="item in options"
      :key="item.key">
      {{ item.label }}({{ getCountData(item.tabsCountKey) }})
    </p>
  </div>
</template>

<script setup>
defineOptions({
  name: "TabsItem",
});
const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
  countData: {
    type: Object,
    default: () => ({}),
  },
});
const getCountData = (key) => {
  return Reflect.get(props.countData?.value || {}, key) || 0;
};
const businessActive = defineModel("businessActive");
const businessIndicatorLeft = computed(() => {
  // 动态计算指示器位置
  const totalOptions = props.options.length;
  if (totalOptions === 0) return "-50%"; // 不显示

  // 查找当前激活项的索引
  const activeIndex = props.options.findIndex((option) => option.key === businessActive.value);
  if (activeIndex === -1) return "-50%"; // 未找到时不显示

  // 计算每个选项占据的宽度百分比
  const optionWidth = 100 / totalOptions;

  // 计算指示器的中心位置（选项中心）
  const indicatorPosition = (activeIndex + 0.5) * optionWidth;

  return `${indicatorPosition}%`;
});
</script>

<style lang="scss" scoped>
.business-tabs {
  &::after {
    content: "";
    position: absolute;
    left: var(--indicator-left, 12.5%);
    bottom: 0;
    width: 24px;
    height: 4px;
    background: #0e5fff;
    transform: translateX(-50%);
    transition: all 0.3s ease-in-out;
  }
}
</style>
