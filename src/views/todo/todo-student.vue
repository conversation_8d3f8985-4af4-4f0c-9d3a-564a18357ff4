<template>
  <div class="ssw-todo h-full w-full flex flex-col">
    <div class="bg-white">
      <div class="relative h-[88px] flex items-center">
        <p
          class="flex-1 h-full box-border text-center pt-[20px] text-[24px] text-[#BFBFBF] font-normal font-pingfang"
          v-for="item in todoTabsOptions"
          :key="item.key"
          :class="{ '!text-[#232528]': activeTab === item.key }"
          @click="onTabClick(item.key)">
          {{ item.label }}
        </p>
        <img
          :src="$getAssetsFile('tabs-active-icon.png')"
          alt=""
          class="w-3 h-1.5 absolute bottom-1.5 translate-x-[-50%] transition-all duration-300"
          :class="todoTabsOptions.find((item) => item.key === activeTab)?.class" />
      </div>
      <p class="bg-[#FFF8F0] p-2.5 text-[#EC8800] text-[22px] font-normal font-pingfang">【温馨提示】以下预警信息基于教务系统产生，最终解释权在教务处。</p>
      <SearchItem v-model:searchParams="searchParams" :options="searchOptions" />
      <TabsItem :options="tabsOptions" :countData="todoPageCount" :businessActive="businessActive" @change="onTabBusinessClick" />
    </div>

    <div class="flex-1 min-h-0 py-[24px]">
      <div class="h-full overflow-y-auto px-[26px]">
        <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="flex flex-col gap-y-[30px]">
            <ListItem v-for="item in list" :key="item" :options="listOptionsKey" btnName="处理" :data="item" @btnClick="onListItemClick(item)">
              <template #title>
                <span>预警学生：{{ item.xsxm }}({{ item.xsxh }})</span>
              </template>
              <template #kcmc="{ data }">
                <span class="text-[#222] text-[24px] font-normal flex-1">{{ data.kcxnh }}/{{ data.kcxqh }}</span>
              </template>
            </ListItem>
          </div>
        </van-list>
      </div>
    </div>
  </div>
</template>

<script setup>
import SearchItem from "@/views/todo/components/search-item.vue";
import TabsItem from "@/views/todo/components/tabs-item.vue";
import ListItem from "./components/list-item.vue";
import { getTodoPage, getTodoPageCount } from "@/api/todo";
import { reactive } from "vue";
defineOptions({
  name: "TodoStudent",
});
definePage({
  name: "TodoStudent",
  meta: {
    layout: "index",
    title: "待办",
    navBar: true,
    isAuth: false,
  },
});
const route = useRoute();

// 搜索参数
const searchParams = reactive({
  xsxm: "",
});

// 搜索参数选项
const searchOptions = ref([
  {
    key: "xm",
    label: "姓名",
    value: "xsxm",
    type: "input",
    placeholder: "搜索姓名",
  },
]);

const activeTab = ref("0");
const todoTabsOptions = readonly([
  {
    label: "待办",
    key: "0",
    class: "left-[25%]",
  },
  {
    label: "已办",
    key: "1",
    class: "left-[75%]",
  },
]);
/**
 * @description 切换待办已办tabs
 * @param {string} key
 */
const onTabClick = (key) => {
  businessActive.value = "1";
  searchParams.xsxm = "";
  sendTodoPageCount({ ...searchParams, clzt: key });
  activeTab.value = key;
  onRefresh();
};

// 业务类型选择
const { data: todoPageCount, send: sendTodoPageCount } = getTodoPageCount(route.query.yjbh);

// 当搜索参数变化时，刷新列表和待办已办数量
watch(searchParams, (newVal) => {
  onRefresh();
  sendTodoPageCount({ ...newVal, clzt: activeTab.value });
});
const businessActive = ref("1");
const listOptionsKey = computed(() => {
  return Reflect.get(listOptions.value, tabsOptions.value.find((item) => item.key === businessActive.value)?.listOptionsKey);
});
// 业务类型选择选项
const tabsOptions = ref([
  {
    label: "必修",
    key: "1",
    tabsCountKey: "cjyjsl",
    listOptionsKey: "bx",
  },
  {
    label: "选修",
    key: "4",
    tabsCountKey: "xxyjsl",
    listOptionsKey: "xx",
  },
  {
    label: "实习",
    key: "2",
    tabsCountKey: "dgsxyjsl",
    listOptionsKey: "bx",
  },
  {
    label: "毕设",
    key: "3",
    tabsCountKey: "bysjyjsl",
    listOptionsKey: "bx",
  },
]);

// 列表选项
const listOptions = ref({
  bx: [
    {
      label: "课程名称",
      value: "kcmc",
    },
    {
      label: "开课学年/学期",
      slot: "kcmc",
    },
    {
      label: "实得成绩",
      value: "sdcj",
    },
    {
      label: "成绩类型",
      value: "cjlxmc",
    },
  ],
  xx: [
    {
      label: "已修课程",
      value: "xxhgs",
    },
    {
      label: "待修课程",
      value: "xxqks",
    },
  ],
});

const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const pageCurrent = ref(1);
const onLoad = () => {
  getTodoPage({
    current: pageCurrent.value,
    pageSize: 10,
    yjlx: businessActive.value,
    ...searchParams,
    clzt: activeTab.value,
  }).then((res) => {
    if (pageCurrent.value === 1) {
      list.value = res.records;
    } else {
      list.value.push(...res.records);
    }
    // 加载状态结束
    loading.value = false;
    if (res.total <= list.value.length) {
      finished.value = true;
    } else {
      pageCurrent.value++;
    }
  });
};
const onRefresh = () => {
  pageCurrent.value = 1;
  list.value = [];
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  onLoad();
};

const onTabBusinessClick = (key) => {
  businessActive.value = key;
  onRefresh();
};

const router = useRouter();
const onListItemClick = (item) => {
  if (businessActive.value === "4") {
    router.push({
      path: "/todo/take-details",
      query: {
        id: item.id,
        yjbh: item.yjbh,
        xsxh: item.xsxh,
        sfxx: businessActive.value === "4" ? 1 : 0, // 是否选修 1是 0否
        kcbh: item.kcbh,
        type: "take", // detail 详情，take 处理
      },
    });
  } else {
    router.push({
      path: "/todo/details",
      query: {
        id: item.id,
        yjbh: item.yjbh,
        xsxh: item.xsxh,
        sfxx: businessActive.value === "4" ? 1 : 0, // 是否选修 1是 0否
        kcbh: item.kcbh,
        type: "take", // detail 详情，take 处理
      },
    });
  }
};
</script>

<style lang="scss" scoped>
.ssw-todo {
  background: #f0f2f9;
  font-size: 14px;
  .business-tabs {
    &::after {
      content: "";
      position: absolute;
      left: var(--indicator-left, 12.5%);
      bottom: 0;
      width: 24px;
      height: 4px;
      background: #0e5fff;
      transform: translateX(-50%);
      transition: all 0.3s ease-in-out;
    }
  }
}
</style>
