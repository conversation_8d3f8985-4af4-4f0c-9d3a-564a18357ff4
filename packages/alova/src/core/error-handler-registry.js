/**
 * 错误处理器注册表
 *
 * 管理所有错误处理器的注册、查找和调度
 * 实现职责链模式，按优先级处理错误
 *
 * <AUTHOR>
 * @version 2.0.0
 */

import logger from '../utils/logger.js'
import {
  BusinessErrorHandler,
  HttpStatusErrorHandler,
  NetworkErrorHandler,
  TimeoutErrorHandler,
  UnknownErrorHandler,
} from './error-handlers.js'

/**
 * 错误处理器注册表类
 *
 * 负责管理和调度所有错误处理器
 * 使用责任链模式依次尝试处理错误
 */
class ErrorHandlerRegistry {
  constructor() {
    /** @type {Array<BaseErrorHandler>} 错误处理器列表 */
    this.handlers = []

    /** @type {Map<string, BaseErrorHandler>} 按类型索引的处理器映射 */
    this.handlerMap = new Map()

    /** @type {object} 注册表统计信息 */
    this.stats = {
      totalHandled: 0,
      handlerStats: {},
      lastResetTime: Date.now(),
    }

    // 初始化默认错误处理器
    this._initializeDefaultHandlers()
  }

  /**
   * 初始化默认错误处理器
   * 按优先级顺序注册处理器
   *
   * @private
   */
  _initializeDefaultHandlers() {
    // 按优先级顺序注册（优先级数值越大，优先级越高）
    this.register(new NetworkErrorHandler(), 50) // 网络错误（优先级最高）
    this.register(new TimeoutErrorHandler(), 40) // 超时错误
    this.register(new HttpStatusErrorHandler(), 30) // HTTP状态错误
    this.register(new BusinessErrorHandler(), 20) // 业务错误
    this.register(new UnknownErrorHandler(), 10) // 兜底处理器（优先级最低）
  }

  /**
   * 注册错误处理器
   *
   * @param {BaseErrorHandler} handler - 错误处理器实例
   * @param {number} priority - 优先级（数值越大优先级越高）
   * @returns {ErrorHandlerRegistry} 链式调用支持
   */
  register(handler, priority = 0) {
    if (!handler || typeof handler.handle !== 'function') {
      logger.warn('尝试注册无效的错误处理器:', handler)
      return this
    }

    // 检查是否已存在同类型处理器
    if (this.handlerMap.has(handler.errorType)) {
      logger.warn(`错误处理器类型 ${handler.errorType} 已存在，将替换现有处理器`)
      this.unregister(handler.errorType)
    }

    // 添加优先级属性
    handler.priority = priority

    // 按优先级插入到正确位置
    const insertIndex = this._findInsertIndex(priority)
    this.handlers.splice(insertIndex, 0, handler)

    // 更新索引映射
    this.handlerMap.set(handler.errorType, handler)

    // 初始化统计信息
    this.stats.handlerStats[handler.errorType] = {
      registered: true,
      handledCount: 0,
      lastHandledTime: null,
    }

    logger.debug(`注册错误处理器: ${handler.errorType}, 优先级: ${priority}`)
    return this
  }

  /**
   * 注销错误处理器
   *
   * @param {string} errorType - 错误类型
   * @returns {boolean} 是否成功注销
   */
  unregister(errorType) {
    const handler = this.handlerMap.get(errorType)
    if (!handler) {
      return false
    }

    // 从列表中移除
    const index = this.handlers.indexOf(handler)
    if (index > -1) {
      this.handlers.splice(index, 1)
    }

    // 从映射中移除
    this.handlerMap.delete(errorType)

    // 更新统计信息
    if (this.stats.handlerStats[errorType]) {
      this.stats.handlerStats[errorType].registered = false
    }

    logger.debug(`注销错误处理器: ${errorType}`)
    return true
  }

  /**
   * 处理错误
   *
   * 按优先级顺序尝试各个处理器，直到找到能处理的处理器
   *
   * @param {Error} error - 错误对象
   * @param {string} customMessage - 自定义错误消息
   * @param {object} context - 错误上下文
   * @returns {object} 错误处理结果
   */
  handle(error, customMessage = '', context = {}) {
    let handledResult = null
    let handledBy = null
    // 按优先级顺序尝试处理器
    for (const handler of this.handlers) {
      try {
        if (handler.canHandle(error)) {
          handledResult = handler.handle(error, customMessage, context)
          handledBy = handler.errorType

          // 更新统计信息
          this._updateStats(handler.errorType)
          break
        }
      }
      catch (handlerError) {
        logger.error(`错误处理器 ${handler.errorType} 处理时发生异常:`, handlerError)
        // 继续尝试下一个处理器
      }
    }

    // 如果没有处理器能处理，使用兜底逻辑
    if (!handledResult) {
      const fallbackMessage = customMessage || error?.message || '未知错误'
      handledResult = {
        message: fallbackMessage,
        severity: 'unknown',
        metadata: {
          handlerType: 'fallback',
          error: 'no_handler_found',
        },
      }
      handledBy = 'fallback'
      logger.warn('没有找到合适的错误处理器，使用兜底处理', { error, customMessage })
    }

    // 记录处理结果
    logger.debug(`错误已由 ${handledBy} 处理:`, {
      errorType: error?.name,
      message: handledResult.message,
      severity: handledResult.severity,
    })

    return handledResult
  }

  /**
   * 获取指定类型的错误处理器
   *
   * @param {string} errorType - 错误类型
   * @returns {BaseErrorHandler|null} 错误处理器实例
   */
  getHandler(errorType) {
    return this.handlerMap.get(errorType) || null
  }

  /**
   * 获取所有已注册的处理器
   *
   * @returns {Array<BaseErrorHandler>} 处理器列表副本
   */
  getAllHandlers() {
    return [...this.handlers]
  }

  /**
   * 检查是否已注册指定类型的处理器
   *
   * @param {string} errorType - 错误类型
   * @returns {boolean} 是否已注册
   */
  hasHandler(errorType) {
    return this.handlerMap.has(errorType)
  }

  /**
   * 获取注册表统计信息
   *
   * @returns {object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      handlerCount: this.handlers.length,
      registeredTypes: Array.from(this.handlerMap.keys()),
      individualStats: Object.fromEntries(
        Array.from(this.handlerMap.entries()).map(([type, handler]) => [
          type,
          {
            ...this.stats.handlerStats[type],
            ...handler.getStats(),
          },
        ]),
      ),
    }
  }

  /**
   * 重置所有统计信息
   */
  resetStats() {
    this.stats.totalHandled = 0
    this.stats.lastResetTime = Date.now()

    // 重置各处理器统计
    this.handlers.forEach((handler) => {
      handler.resetStats()
      if (this.stats.handlerStats[handler.errorType]) {
        this.stats.handlerStats[handler.errorType].handledCount = 0
        this.stats.handlerStats[handler.errorType].lastHandledTime = null
      }
    })

    logger.debug('错误处理器注册表统计信息已重置')
  }

  /**
   * 查找插入位置（按优先级排序）
   *
   * @private
   * @param {number} priority - 优先级
   * @returns {number} 插入位置索引
   */
  _findInsertIndex(priority) {
    for (let i = 0; i < this.handlers.length; i++) {
      if (this.handlers[i].priority < priority) {
        return i
      }
    }
    return this.handlers.length
  }

  /**
   * 更新统计信息
   *
   * @private
   * @param {string} errorType - 错误类型
   */
  _updateStats(errorType) {
    this.stats.totalHandled++

    if (this.stats.handlerStats[errorType]) {
      this.stats.handlerStats[errorType].handledCount++
      this.stats.handlerStats[errorType].lastHandledTime = Date.now()
    }
  }
}

// 创建单例实例
const errorHandlerRegistry = new ErrorHandlerRegistry()

export { ErrorHandlerRegistry }
export default errorHandlerRegistry
